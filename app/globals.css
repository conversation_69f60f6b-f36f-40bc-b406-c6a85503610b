@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 255, 255, 255;
        --background-start-rgb: 0, 0, 0;
        --background-end-rgb: 0, 0, 0;
    }
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }

    /* Design System Utilities */
    .ds-transition {
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
    }

    .ds-animation {
        animation-duration: var(--animation-duration, 150ms);
        animation-timing-function: var(--animation-easing, cubic-bezier(0.4, 0, 0.2, 1));
    }

    .ds-shadow {
        box-shadow: var(--shadow-md);
    }

    .ds-focus-ring {
        @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    }

    /* Responsive typography */
    .ds-text-responsive {
        font-size: clamp(0.875rem, 2.5vw, 1rem);
        line-height: 1.5;
    }

    .ds-heading-responsive {
        font-size: clamp(1.25rem, 4vw, 2rem);
        line-height: 1.2;
    }

    /* Typography scale */
    .text-xs-responsive {
        font-size: clamp(0.625rem, 1.5vw, 0.75rem);
    }

    .text-sm-responsive {
        font-size: clamp(0.75rem, 2vw, 0.875rem);
    }

    .text-base-responsive {
        font-size: clamp(0.875rem, 2.5vw, 1rem);
    }

    .text-lg-responsive {
        font-size: clamp(1rem, 3vw, 1.125rem);
    }

    .text-xl-responsive {
        font-size: clamp(1.125rem, 3.5vw, 1.25rem);
    }

    .text-2xl-responsive {
        font-size: clamp(1.25rem, 4vw, 1.5rem);
    }

    .text-3xl-responsive {
        font-size: clamp(1.5rem, 5vw, 1.875rem);
    }

    .text-4xl-responsive {
        font-size: clamp(1.875rem, 6vw, 2.25rem);
    }

    /* Improved readability */
    .text-readable {
        line-height: 1.7;
        letter-spacing: 0.01em;
    }

    .text-tight {
        line-height: 1.25;
        letter-spacing: -0.025em;
    }

    .text-loose {
        line-height: 1.8;
        letter-spacing: 0.025em;
    }
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: 240 5.9% 10%;
        --primary-foreground: 0 0% 98%;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;
        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;
        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }

    /* Design System Customization Classes */
    .no-animations * {
        animation-duration: 0ms !important;
        animation-delay: 0ms !important;
        transition-duration: 0ms !important;
        transition-delay: 0ms !important;
    }

    .no-transitions * {
        transition: none !important;
    }

    .no-shadows * {
        box-shadow: none !important;
        filter: none !important;
    }

    .compact-mode {
        --spacing-scale: 0.75;
    }

    .compact-mode .p-4 {
        padding: calc(1rem * var(--spacing-scale));
    }

    .compact-mode .gap-4 {
        gap: calc(1rem * var(--spacing-scale));
    }

    .compact-mode .space-y-4>*+* {
        margin-top: calc(1rem * var(--spacing-scale));
    }

    .high-contrast {
        --contrast-multiplier: 1.5;
    }

    .high-contrast * {
        border-width: 2px;
    }

    .high-contrast .border {
        border-color: hsl(var(--foreground)) !important;
    }

    .high-contrast .bg-muted {
        background-color: hsl(var(--background)) !important;
        border: 2px solid hsl(var(--foreground)) !important;
    }

    /* Enhanced Animations */
    @keyframes slideInFromLeft {
        from {
            opacity: 0;
            transform: translateX(-100%);
        }

        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInFromRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }

        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInFromTop {
        from {
            opacity: 0;
            transform: translateY(-100%);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInFromBottom {
        from {
            opacity: 0;
            transform: translateY(100%);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.95);
        }

        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes bounceIn {
        0% {
            opacity: 0;
            transform: scale(0.3);
        }

        50% {
            opacity: 1;
            transform: scale(1.05);
        }

        70% {
            transform: scale(0.9);
        }

        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes shimmer {
        0% {
            background-position: -200px 0;
        }

        100% {
            background-position: calc(200px + 100%) 0;
        }
    }

    /* Animation utility classes */
    .animate-slide-in-left {
        animation: slideInFromLeft 0.3s ease-out;
    }

    .animate-slide-in-right {
        animation: slideInFromRight 0.3s ease-out;
    }

    .animate-slide-in-top {
        animation: slideInFromTop 0.3s ease-out;
    }

    .animate-slide-in-bottom {
        animation: slideInFromBottom 0.3s ease-out;
    }

    .animate-fade-in-scale {
        animation: fadeInScale 0.2s ease-out;
    }

    .animate-bounce-in {
        animation: bounceIn 0.5s ease-out;
    }

    .animate-shimmer {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }

    /* Interactive states */
    .interactive-scale {
        transition: transform 0.2s ease-in-out;
    }

    .interactive-scale:hover {
        transform: scale(1.02);
    }

    .interactive-scale:active {
        transform: scale(0.98);
    }

    .interactive-lift {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .interactive-lift:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .interactive-glow {
        transition: box-shadow 0.2s ease-in-out;
    }

    .interactive-glow:hover {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }

    .interactive-glow:focus-visible {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
}

.skeleton {
    * {
        pointer-events: none !important;
    }

    *[class^="text-"] {
        color: transparent;
        @apply rounded-md bg-foreground/20 select-none animate-pulse;
    }

    .skeleton-bg {
        @apply bg-foreground/10;
    }

    .skeleton-div {
        @apply bg-foreground/20 animate-pulse;
    }
}

.ProseMirror {
    outline: none;
}

.cm-editor,
.cm-gutters {
    @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
    @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
    @apply bg-transparent !important;
}

.cm-activeLine {
    @apply rounded-r-sm !important;
}

.cm-lineNumbers {
    @apply min-w-7;
}

.cm-foldGutter {
    @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
    @apply rounded-l-sm !important;
}

.suggestion-highlight {
    @apply bg-blue-200 hover:bg-blue-300 dark:hover:bg-blue-400/50 dark:text-blue-50 dark:bg-blue-500/40;
}