/* Design System Utility Classes */
/* These utilities are built on top of design tokens */

@layer utilities {
  /* Spacing Utilities */
  .ds-p-0 { padding: var(--spacing-0); }
  .ds-p-1 { padding: var(--spacing-1); }
  .ds-p-2 { padding: var(--spacing-2); }
  .ds-p-3 { padding: var(--spacing-3); }
  .ds-p-4 { padding: var(--spacing-4); }
  .ds-p-5 { padding: var(--spacing-5); }
  .ds-p-6 { padding: var(--spacing-6); }
  .ds-p-8 { padding: var(--spacing-8); }
  .ds-p-10 { padding: var(--spacing-10); }
  .ds-p-12 { padding: var(--spacing-12); }

  .ds-m-0 { margin: var(--spacing-0); }
  .ds-m-1 { margin: var(--spacing-1); }
  .ds-m-2 { margin: var(--spacing-2); }
  .ds-m-3 { margin: var(--spacing-3); }
  .ds-m-4 { margin: var(--spacing-4); }
  .ds-m-5 { margin: var(--spacing-5); }
  .ds-m-6 { margin: var(--spacing-6); }
  .ds-m-8 { margin: var(--spacing-8); }
  .ds-m-10 { margin: var(--spacing-10); }
  .ds-m-12 { margin: var(--spacing-12); }

  .ds-gap-1 { gap: var(--spacing-1); }
  .ds-gap-2 { gap: var(--spacing-2); }
  .ds-gap-3 { gap: var(--spacing-3); }
  .ds-gap-4 { gap: var(--spacing-4); }
  .ds-gap-5 { gap: var(--spacing-5); }
  .ds-gap-6 { gap: var(--spacing-6); }
  .ds-gap-8 { gap: var(--spacing-8); }

  /* Typography Utilities */
  .ds-text-xs { font-size: var(--font-size-xs); }
  .ds-text-sm { font-size: var(--font-size-sm); }
  .ds-text-base { font-size: var(--font-size-base); }
  .ds-text-lg { font-size: var(--font-size-lg); }
  .ds-text-xl { font-size: var(--font-size-xl); }
  .ds-text-2xl { font-size: var(--font-size-2xl); }
  .ds-text-3xl { font-size: var(--font-size-3xl); }
  .ds-text-4xl { font-size: var(--font-size-4xl); }

  .ds-font-thin { font-weight: var(--font-weight-thin); }
  .ds-font-light { font-weight: var(--font-weight-light); }
  .ds-font-normal { font-weight: var(--font-weight-normal); }
  .ds-font-medium { font-weight: var(--font-weight-medium); }
  .ds-font-semibold { font-weight: var(--font-weight-semibold); }
  .ds-font-bold { font-weight: var(--font-weight-bold); }

  .ds-leading-none { line-height: var(--line-height-none); }
  .ds-leading-tight { line-height: var(--line-height-tight); }
  .ds-leading-normal { line-height: var(--line-height-normal); }
  .ds-leading-relaxed { line-height: var(--line-height-relaxed); }

  .ds-tracking-tight { letter-spacing: var(--letter-spacing-tight); }
  .ds-tracking-normal { letter-spacing: var(--letter-spacing-normal); }
  .ds-tracking-wide { letter-spacing: var(--letter-spacing-wide); }

  /* Color Utilities */
  .ds-text-primary { color: var(--color-text-primary); }
  .ds-text-secondary { color: var(--color-text-secondary); }
  .ds-text-disabled { color: var(--color-text-disabled); }
  .ds-text-inverse { color: var(--color-text-inverse); }

  .ds-bg-primary { background-color: var(--color-bg-primary); }
  .ds-bg-secondary { background-color: var(--color-bg-secondary); }
  .ds-bg-tertiary { background-color: var(--color-bg-tertiary); }
  .ds-bg-inverse { background-color: var(--color-bg-inverse); }

  .ds-border-primary { border-color: var(--color-border-primary); }
  .ds-border-secondary { border-color: var(--color-border-secondary); }
  .ds-border-focus { border-color: var(--color-border-focus); }

  /* Border Radius Utilities */
  .ds-rounded-none { border-radius: var(--border-radius-none); }
  .ds-rounded-sm { border-radius: var(--border-radius-sm); }
  .ds-rounded { border-radius: var(--border-radius-base); }
  .ds-rounded-md { border-radius: var(--border-radius-md); }
  .ds-rounded-lg { border-radius: var(--border-radius-lg); }
  .ds-rounded-xl { border-radius: var(--border-radius-xl); }
  .ds-rounded-2xl { border-radius: var(--border-radius-2xl); }
  .ds-rounded-full { border-radius: var(--border-radius-full); }

  /* Shadow Utilities */
  .ds-shadow-none { box-shadow: var(--shadow-none); }
  .ds-shadow-xs { box-shadow: var(--shadow-xs); }
  .ds-shadow-sm { box-shadow: var(--shadow-sm); }
  .ds-shadow { box-shadow: var(--shadow-base); }
  .ds-shadow-md { box-shadow: var(--shadow-md); }
  .ds-shadow-lg { box-shadow: var(--shadow-lg); }
  .ds-shadow-xl { box-shadow: var(--shadow-xl); }
  .ds-shadow-inner { box-shadow: var(--shadow-inner); }

  /* Animation Utilities */
  .ds-duration-75 { transition-duration: var(--duration-75); }
  .ds-duration-100 { transition-duration: var(--duration-100); }
  .ds-duration-150 { transition-duration: var(--duration-150); }
  .ds-duration-200 { transition-duration: var(--duration-200); }
  .ds-duration-300 { transition-duration: var(--duration-300); }
  .ds-duration-500 { transition-duration: var(--duration-500); }

  .ds-ease-linear { transition-timing-function: var(--easing-linear); }
  .ds-ease-in { transition-timing-function: var(--easing-in); }
  .ds-ease-out { transition-timing-function: var(--easing-out); }
  .ds-ease-in-out { transition-timing-function: var(--easing-in-out); }

  /* Layout Utilities */
  .ds-container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
  }

  @media (min-width: 640px) {
    .ds-container { max-width: var(--container-sm); }
  }

  @media (min-width: 768px) {
    .ds-container { max-width: var(--container-md); }
  }

  @media (min-width: 1024px) {
    .ds-container { max-width: var(--container-lg); }
  }

  @media (min-width: 1280px) {
    .ds-container { max-width: var(--container-xl); }
  }

  @media (min-width: 1536px) {
    .ds-container { max-width: var(--container-2xl); }
  }

  /* Z-Index Utilities */
  .ds-z-auto { z-index: var(--z-index-auto); }
  .ds-z-0 { z-index: var(--z-index-0); }
  .ds-z-10 { z-index: var(--z-index-10); }
  .ds-z-20 { z-index: var(--z-index-20); }
  .ds-z-30 { z-index: var(--z-index-30); }
  .ds-z-40 { z-index: var(--z-index-40); }
  .ds-z-50 { z-index: var(--z-index-50); }
  .ds-z-dropdown { z-index: var(--z-index-dropdown); }
  .ds-z-modal { z-index: var(--z-index-modal); }
  .ds-z-tooltip { z-index: var(--z-index-tooltip); }

  /* Interactive State Utilities */
  .ds-interactive {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: var(--easing-out);
    transition-duration: var(--duration-150);
  }

  .ds-interactive:hover {
    opacity: var(--opacity-hover);
  }

  .ds-interactive:active {
    opacity: var(--opacity-pressed);
  }

  .ds-interactive:disabled {
    opacity: var(--opacity-disabled);
    cursor: not-allowed;
  }

  /* Focus Utilities */
  .ds-focus-ring {
    outline: none;
  }

  .ds-focus-ring:focus-visible {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
  }

  /* Component Utilities */
  .ds-button-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    font-weight: var(--font-weight-medium);
    border-radius: var(--border-radius-md);
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: var(--easing-out);
    transition-duration: var(--duration-150);
  }

  .ds-button-base:focus-visible {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
  }

  .ds-button-base:disabled {
    opacity: var(--opacity-disabled);
    cursor: not-allowed;
  }

  .ds-input-base {
    display: flex;
    width: 100%;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-border-primary);
    background-color: var(--color-bg-primary);
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: var(--easing-out);
    transition-duration: var(--duration-150);
  }

  .ds-input-base:focus-visible {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
    border-color: var(--color-border-focus);
  }

  .ds-input-base:disabled {
    opacity: var(--opacity-disabled);
    cursor: not-allowed;
  }

  .ds-card-base {
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border-primary);
    background-color: var(--color-bg-tertiary);
    box-shadow: var(--shadow-sm);
  }

  /* Responsive Utilities */
  @media (max-width: 640px) {
    .ds-mobile-hidden { display: none; }
    .ds-mobile-full { width: 100%; }
  }

  @media (min-width: 641px) {
    .ds-desktop-hidden { display: none; }
  }

  /* Print Utilities */
  @media print {
    .ds-print-hidden { display: none !important; }
    .ds-print-visible { display: block !important; }
  }
}
