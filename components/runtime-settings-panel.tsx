'use client';

import { useState, useEffect } from 'react';
import { useDesignSystemContext } from './theme-provider';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Label } from './ui/label';
import { Switch } from './ui/switch';
import { Slider } from './ui/slider';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from './ui/sheet';
import { SettingsIcon, DownloadIcon, UploadIcon, CopyIcon } from './icons';
import { ThemeSelector } from './theme-selector';
import { toast } from './toast';

export function RuntimeSettingsPanel() {
  const { config, updateConfig, resetToDefaults } = useDesignSystemContext();
  const [isOpen, setIsOpen] = useState(false);
  const [customCSS, setCustomCSS] = useState('');
  const [configJSON, setConfigJSON] = useState('');

  useEffect(() => {
    setConfigJSON(JSON.stringify(config, null, 2));
  }, [config]);

  const handleCustomizationChange = (key: keyof typeof config.customizations, value: boolean) => {
    updateConfig({
      customizations: {
        ...config.customizations,
        [key]: value,
      },
    });
  };

  const handleExportConfig = () => {
    const configBlob = new Blob([configJSON], { type: 'application/json' });
    const url = URL.createObjectURL(configBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'design-system-config.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      type: 'success',
      description: 'Configuration exported successfully',
    });
  };

  const handleImportConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedConfig = JSON.parse(e.target?.result as string);
        updateConfig(importedConfig);
        toast({
          type: 'success',
          description: 'Configuration imported successfully',
        });
      } catch (error) {
        toast({
          type: 'error',
          description: 'Failed to import configuration',
        });
      }
    };
    reader.readAsText(file);
  };

  const handleCopyConfig = async () => {
    try {
      await navigator.clipboard.writeText(configJSON);
      toast({
        type: 'success',
        description: 'Configuration copied to clipboard',
      });
    } catch (error) {
      toast({
        type: 'error',
        description: 'Failed to copy configuration',
      });
    }
  };

  const handleApplyCustomCSS = () => {
    if (!customCSS.trim()) return;

    // Create or update custom CSS style element
    let styleElement = document.getElementById('runtime-custom-css') as HTMLStyleElement;
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'runtime-custom-css';
      document.head.appendChild(styleElement);
    }
    
    styleElement.textContent = customCSS;
    
    toast({
      type: 'success',
      description: 'Custom CSS applied',
    });
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <SettingsIcon className="h-4 w-4" />
          <span className="sr-only">Runtime settings</span>
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Runtime Settings</SheetTitle>
          <SheetDescription>
            Customize the design system in real-time
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* Theme Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Theme</CardTitle>
              <CardDescription>
                Choose your preferred color theme
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ThemeSelector />
            </CardContent>
          </Card>

          {/* Visual Customizations */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Visual Effects</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="rt-animations" className="text-sm">
                  Animations
                </Label>
                <Switch
                  id="rt-animations"
                  checked={config.customizations.enableAnimations}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('enableAnimations', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="rt-transitions" className="text-sm">
                  Transitions
                </Label>
                <Switch
                  id="rt-transitions"
                  checked={config.customizations.enableTransitions}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('enableTransitions', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="rt-shadows" className="text-sm">
                  Shadows
                </Label>
                <Switch
                  id="rt-shadows"
                  checked={config.customizations.enableShadows}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('enableShadows', checked)
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Layout Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Layout</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="rt-compact" className="text-sm">
                  Compact Mode
                </Label>
                <Switch
                  id="rt-compact"
                  checked={config.customizations.compactMode}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('compactMode', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="rt-contrast" className="text-sm">
                  High Contrast
                </Label>
                <Switch
                  id="rt-contrast"
                  checked={config.customizations.highContrast}
                  onCheckedChange={(checked) => 
                    handleCustomizationChange('highContrast', checked)
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Custom CSS */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Custom CSS</CardTitle>
              <CardDescription>
                Add custom styles that will be applied immediately
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="/* Add your custom CSS here */
.custom-class {
  color: red;
}"
                value={customCSS}
                onChange={(e) => setCustomCSS(e.target.value)}
                className="font-mono text-sm"
                rows={6}
              />
              <Button onClick={handleApplyCustomCSS} className="w-full">
                Apply Custom CSS
              </Button>
            </CardContent>
          </Card>

          {/* Configuration Management */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Configuration</CardTitle>
              <CardDescription>
                Export, import, or copy your current configuration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleExportConfig} className="flex-1">
                  <DownloadIcon className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" className="flex-1" asChild>
                  <label htmlFor="import-config">
                    <UploadIcon className="h-4 w-4 mr-2" />
                    Import
                  </label>
                </Button>
                <input
                  id="import-config"
                  type="file"
                  accept=".json"
                  onChange={handleImportConfig}
                  className="hidden"
                />
                <Button variant="outline" onClick={handleCopyConfig}>
                  <CopyIcon className="h-4 w-4" />
                </Button>
              </div>
              
              <Textarea
                value={configJSON}
                onChange={(e) => setConfigJSON(e.target.value)}
                className="font-mono text-xs"
                rows={8}
                readOnly
              />
            </CardContent>
          </Card>

          {/* Reset */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Reset</CardTitle>
            </CardHeader>
            <CardContent>
              <Button 
                variant="destructive" 
                onClick={resetToDefaults}
                className="w-full"
              >
                Reset to Defaults
              </Button>
            </CardContent>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  );
}
