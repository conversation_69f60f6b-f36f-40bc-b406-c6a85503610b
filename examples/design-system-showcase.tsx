'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { ThemeSelector, CompactThemeSelector } from '@/components/theme-selector';
import { DesignSystemSettings } from '@/components/design-system-settings';
import { RuntimeSettingsPanel } from '@/components/runtime-settings-panel';
import { useDesignSystemContext } from '@/components/theme-provider';
import { toast } from '@/components/toast';
import { PlusIcon, SettingsIcon, CheckIcon } from '@/components/icons';

export function DesignSystemShowcase() {
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [textareaValue, setTextareaValue] = useState('');
  const [switchValue, setSwitchValue] = useState(false);
  const { currentTheme, config } = useDesignSystemContext();

  const handleButtonClick = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      toast({
        type: 'success',
        description: 'Action completed successfully!',
      });
    }, 2000);
  };

  const showErrorToast = () => {
    toast({
      type: 'error',
      description: 'Something went wrong. Please try again.',
    });
  };

  const showWarningToast = () => {
    toast({
      type: 'warning',
      description: 'This action requires confirmation.',
    });
  };

  const showInfoToast = () => {
    toast({
      type: 'info',
      description: 'Here is some helpful information.',
    });
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl-responsive ds-font-bold ds-text-primary">
            Design System Showcase
          </h1>
          <p className="text-lg-responsive ds-text-secondary max-w-2xl mx-auto">
            Explore the comprehensive design system with modular components, 
            theming capabilities, and customization options.
          </p>
          <div className="flex justify-center gap-4">
            <ThemeSelector />
            <DesignSystemSettings />
            <RuntimeSettingsPanel />
          </div>
        </div>

        {/* Current Theme Info */}
        <Card variant="elevated" className="animate-fade-in-scale">
          <CardHeader>
            <CardTitle>Current Configuration</CardTitle>
            <CardDescription>
              Active theme and customization settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label className="text-sm font-medium">Theme</Label>
                <p className="text-lg font-semibold">{currentTheme.displayName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Animations</Label>
                <p className="text-lg font-semibold">
                  {config.customizations.enableAnimations ? 'Enabled' : 'Disabled'}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Compact Mode</Label>
                <p className="text-lg font-semibold">
                  {config.customizations.compactMode ? 'On' : 'Off'}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">High Contrast</Label>
                <p className="text-lg font-semibold">
                  {config.customizations.highContrast ? 'On' : 'Off'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Button Variants */}
        <Card>
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
            <CardDescription>
              Various button styles and states
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Primary Buttons */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Primary Variants</Label>
              <div className="flex flex-wrap gap-3">
                <Button variant="default">Default</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="link">Link</Button>
              </div>
            </div>

            {/* Semantic Buttons */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Semantic Variants</Label>
              <div className="flex flex-wrap gap-3">
                <Button variant="success" onClick={handleButtonClick}>
                  Success
                </Button>
                <Button variant="warning" onClick={showWarningToast}>
                  Warning
                </Button>
                <Button variant="destructive" onClick={showErrorToast}>
                  Destructive
                </Button>
                <Button variant="info" onClick={showInfoToast}>
                  Info
                </Button>
              </div>
            </div>

            {/* Special Variants */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Special Variants</Label>
              <div className="flex flex-wrap gap-3">
                <Button variant="gradient">Gradient</Button>
                <Button variant="elevated" className="interactive-lift">
                  Elevated
                </Button>
              </div>
            </div>

            {/* Button Sizes */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Sizes</Label>
              <div className="flex flex-wrap items-center gap-3">
                <Button size="xs">Extra Small</Button>
                <Button size="sm">Small</Button>
                <Button size="default">Default</Button>
                <Button size="lg">Large</Button>
                <Button size="xl">Extra Large</Button>
              </div>
            </div>

            {/* Button with Icons */}
            <div>
              <Label className="text-sm font-medium mb-3 block">With Icons</Label>
              <div className="flex flex-wrap gap-3">
                <Button leftIcon={<PlusIcon />}>Add Item</Button>
                <Button rightIcon={<SettingsIcon />}>Settings</Button>
                <Button 
                  loading={loading} 
                  loadingText="Processing..."
                  onClick={handleButtonClick}
                >
                  {loading ? 'Processing...' : 'Process'}
                </Button>
              </div>
            </div>

            {/* Icon Buttons */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Icon Buttons</Label>
              <div className="flex flex-wrap gap-3">
                <Button size="icon" variant="outline">
                  <PlusIcon />
                </Button>
                <Button size="icon-sm" variant="ghost">
                  <SettingsIcon />
                </Button>
                <Button size="icon-lg" variant="default">
                  <CheckIcon />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Components */}
        <Card>
          <CardHeader>
            <CardTitle>Form Components</CardTitle>
            <CardDescription>
              Input fields, textareas, and form controls
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Input Variants */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="input-default">Default Input</Label>
                <Input
                  id="input-default"
                  placeholder="Enter text..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="input-filled">Filled Input</Label>
                <Input
                  id="input-filled"
                  variant="filled"
                  placeholder="Filled variant..."
                />
              </div>
              <div>
                <Label htmlFor="input-outlined">Outlined Input</Label>
                <Input
                  id="input-outlined"
                  variant="outlined"
                  placeholder="Outlined variant..."
                />
              </div>
              <div>
                <Label htmlFor="input-error">Error State</Label>
                <Input
                  id="input-error"
                  state="error"
                  placeholder="Error state..."
                  errorText="This field is required"
                />
              </div>
            </div>

            {/* Input with Icons */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="input-left-icon">With Left Icon</Label>
                <Input
                  id="input-left-icon"
                  leftIcon={<PlusIcon />}
                  placeholder="Search..."
                />
              </div>
              <div>
                <Label htmlFor="input-right-icon">With Right Icon</Label>
                <Input
                  id="input-right-icon"
                  rightIcon={<SettingsIcon />}
                  placeholder="Settings..."
                />
              </div>
            </div>

            {/* Textarea */}
            <div>
              <Label htmlFor="textarea">Textarea</Label>
              <Textarea
                id="textarea"
                placeholder="Enter your message..."
                value={textareaValue}
                onChange={(e) => setTextareaValue(e.target.value)}
                maxLength={500}
                showCharCount
                helperText="Maximum 500 characters"
              />
            </div>

            {/* Switch */}
            <div className="flex items-center space-x-2">
              <Switch
                id="switch"
                checked={switchValue}
                onCheckedChange={setSwitchValue}
              />
              <Label htmlFor="switch">Enable notifications</Label>
            </div>
          </CardContent>
        </Card>

        {/* Card Variants */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card variant="default">
            <CardHeader>
              <CardTitle>Default Card</CardTitle>
              <CardDescription>Standard card with border and shadow</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This is the default card variant with standard styling.</p>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardHeader>
              <CardTitle>Outlined Card</CardTitle>
              <CardDescription>Card with prominent border</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card has a thicker border for emphasis.</p>
            </CardContent>
          </Card>

          <Card variant="elevated" className="interactive-lift">
            <CardHeader>
              <CardTitle>Elevated Card</CardTitle>
              <CardDescription>Card with enhanced shadow</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card has enhanced shadows and hover effects.</p>
            </CardContent>
          </Card>

          <Card variant="filled">
            <CardHeader>
              <CardTitle>Filled Card</CardTitle>
              <CardDescription>Card with background fill</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card has a filled background color.</p>
            </CardContent>
          </Card>

          <Card variant="gradient">
            <CardHeader>
              <CardTitle>Gradient Card</CardTitle>
              <CardDescription>Card with gradient background</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card features a subtle gradient background.</p>
            </CardContent>
          </Card>

          <Card variant="ghost">
            <CardHeader>
              <CardTitle>Ghost Card</CardTitle>
              <CardDescription>Minimal card without border</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This card has minimal styling for subtle presentation.</p>
            </CardContent>
          </Card>
        </div>

        {/* Theme Selector Variants */}
        <Card>
          <CardHeader>
            <CardTitle>Theme Selectors</CardTitle>
            <CardDescription>
              Different ways to display theme selection
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-sm font-medium mb-3 block">Full Theme Selector</Label>
              <ThemeSelector />
            </div>
            <div>
              <Label className="text-sm font-medium mb-3 block">Compact Theme Selector</Label>
              <CompactThemeSelector />
            </div>
          </CardContent>
        </Card>

        {/* Utility Classes Demo */}
        <Card>
          <CardHeader>
            <CardTitle>Design System Utilities</CardTitle>
            <CardDescription>
              Examples of utility classes and design tokens
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="ds-p-4 ds-bg-secondary ds-rounded-lg ds-shadow-sm">
              <p className="ds-text-base ds-font-medium">
                This uses design system utility classes
              </p>
            </div>
            <div className="ds-container">
              <p className="ds-text-sm ds-text-secondary">
                Responsive container with design system spacing
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
