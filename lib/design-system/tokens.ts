import type { DesignTokens, ColorScale } from './types';

// Color scales generator
const createColorScale = (
  50: string, 100: string, 200: string, 300: string, 400: string,
  500: string, 600: string, 700: string, 800: string, 900: string, 950: string
): ColorScale => ({
  50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950
});

// Default design tokens
export const defaultTokens: DesignTokens = {
  colors: {
    primary: createColorScale(
      '#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1', '#94a3b8',
      '#64748b', '#475569', '#334155', '#1e293b', '#0f172a', '#020617'
    ),
    secondary: createColorScale(
      '#fafafa', '#f5f5f5', '#e5e5e5', '#d4d4d4', '#a3a3a3',
      '#737373', '#525252', '#404040', '#262626', '#171717', '#0a0a0a'
    ),
    accent: createColorScale(
      '#eff6ff', '#dbeafe', '#bfdbfe', '#93c5fd', '#60a5fa',
      '#3b82f6', '#2563eb', '#1d4ed8', '#1e40af', '#1e3a8a', '#172554'
    ),
    neutral: createColorScale(
      '#fafafa', '#f5f5f5', '#e5e5e5', '#d4d4d4', '#a3a3a3',
      '#737373', '#525252', '#404040', '#262626', '#171717', '#0a0a0a'
    ),
    success: createColorScale(
      '#f0fdf4', '#dcfce7', '#bbf7d0', '#86efac', '#4ade80',
      '#22c55e', '#16a34a', '#15803d', '#166534', '#14532d', '#052e16'
    ),
    warning: createColorScale(
      '#fffbeb', '#fef3c7', '#fde68a', '#fcd34d', '#fbbf24',
      '#f59e0b', '#d97706', '#b45309', '#92400e', '#78350f', '#451a03'
    ),
    error: createColorScale(
      '#fef2f2', '#fecaca', '#fca5a5', '#f87171', '#ef4444',
      '#dc2626', '#b91c1c', '#991b1b', '#7f1d1d', '#450a0a', '#1c0000'
    ),
    info: createColorScale(
      '#eff6ff', '#dbeafe', '#bfdbfe', '#93c5fd', '#60a5fa',
      '#3b82f6', '#2563eb', '#1d4ed8', '#1e40af', '#1e3a8a', '#172554'
    ),
    background: createColorScale(
      '#ffffff', '#fefefe', '#fdfdfd', '#fcfcfc', '#fbfbfb',
      '#fafafa', '#f9f9f9', '#f8f8f8', '#f7f7f7', '#f6f6f6', '#f5f5f5'
    ),
    surface: createColorScale(
      '#ffffff', '#fefefe', '#fdfdfd', '#fcfcfc', '#fbfbfb',
      '#fafafa', '#f9f9f9', '#f8f8f8', '#f7f7f7', '#f6f6f6', '#f5f5f5'
    ),
    overlay: createColorScale(
      'rgba(0,0,0,0.02)', 'rgba(0,0,0,0.05)', 'rgba(0,0,0,0.1)', 'rgba(0,0,0,0.15)', 'rgba(0,0,0,0.2)',
      'rgba(0,0,0,0.3)', 'rgba(0,0,0,0.4)', 'rgba(0,0,0,0.5)', 'rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)', 'rgba(0,0,0,0.9)'
    ),
  },
  
  typography: {
    fontFamily: {
      sans: ['var(--font-geist)', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      serif: ['ui-serif', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
      mono: ['var(--font-geist-mono)', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      '5xl': ['3rem', { lineHeight: '1' }],
      '6xl': ['3.75rem', { lineHeight: '1' }],
    },
    fontWeight: {
      thin: '100',
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900',
    },
  },
  
  spacing: {
    0: '0px',
    px: '1px',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem',
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: '0 0 #0000',
  },
  
  borders: {
    radius: {
      none: '0px',
      sm: '0.125rem',
      base: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      '2xl': '1rem',
      '3xl': '1.5rem',
      full: '9999px',
    },
    width: {
      0: '0px',
      2: '2px',
      4: '4px',
      8: '8px',
    },
  },
  
  animations: {
    duration: {
      75: '75ms',
      100: '100ms',
      150: '150ms',
      200: '200ms',
      300: '300ms',
      500: '500ms',
      700: '700ms',
      1000: '1000ms',
    },
    easing: {
      linear: 'linear',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
};

// Token utilities
export const getColorValue = (colorPath: string, scale: keyof ColorScale = '500'): string => {
  const [colorName] = colorPath.split('.');
  const colorScale = defaultTokens.colors[colorName as keyof typeof defaultTokens.colors];
  return colorScale?.[scale] || colorPath;
};

export const getSpacingValue = (size: keyof typeof defaultTokens.spacing): string => {
  return defaultTokens.spacing[size];
};

export const getFontSize = (size: keyof typeof defaultTokens.typography.fontSize) => {
  return defaultTokens.typography.fontSize[size];
};

export const getShadow = (size: keyof typeof defaultTokens.shadows): string => {
  return defaultTokens.shadows[size];
};

export const getBorderRadius = (size: keyof typeof defaultTokens.borders.radius): string => {
  return defaultTokens.borders.radius[size];
};
